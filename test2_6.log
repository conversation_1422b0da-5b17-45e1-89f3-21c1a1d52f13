QStandardPaths: XDG_RUNTIME_DIR not set, defaulting to '/tmp/runtime-root'
parse-name&ok: 164 true
resive--name: 164
Config path: "/home/<USER>/.config/MyNetworkApp-x86_64.AppImage/config.yaml"
Process available cores: 1
clientName: 164
Available CPU cores: 1
Starting 1 threads with delay
Starting thread 0
Worker 0 started in thread 0x7fdd07284700
ptcp - path "/home/<USER>/tmp/tcp"
client_name:user164 useShm:1 serverAddress:************** serverPort:10081
ptcp_dir_ = /home/<USER>/tmp/tcp
last_server_name_file = /home/<USER>/tmp/tcp/user164.lastserver
shm_send_file: /user164_Server.shm
user164:Login Success
m_statusHandler is nullptr!
Strategy sending completed after 3 minutes
