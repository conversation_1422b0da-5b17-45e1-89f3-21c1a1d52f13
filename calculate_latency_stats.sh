#!/bin/bash

# 计算latency统计数据的脚本
# 输出格式：文件名 条数 平均值 标准差

echo "文件,条数,latency平均值,latency标准差"

for file in test2_1.log test2_2.log test2_4.log test2_5.log test2_6.log; do
    if [ -f "$file" ]; then
        echo "正在处理 $file..." >&2
        
        # 提取latency数值
        latency_values=$(grep "latency:" "$file" | sed 's/.*latency: \([0-9]*\).*/\1/')
        
        if [ -n "$latency_values" ]; then
            # 计算条数
            count=$(echo "$latency_values" | wc -l)
            
            # 计算平均值
            sum=$(echo "$latency_values" | awk '{sum += $1} END {print sum}')
            avg=$(echo "scale=2; $sum / $count" | bc -l)
            
            # 计算标准差
            # 先计算方差
            variance=$(echo "$latency_values" | awk -v avg="$avg" '{sum += ($1 - avg)^2} END {print sum/NR}')
            std_dev=$(echo "scale=2; sqrt($variance)" | bc -l)
            
            echo "$file,$count,$avg,$std_dev"
        else
            echo "$file,0,0,0"
        fi
    else
        echo "$file,文件不存在,0,0"
    fi
done
