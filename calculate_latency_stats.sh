#!/bin/bash

# 计算latency统计数据的脚本
# 输出格式：文件名 条数 平均值 标准差
# 加强版：处理各种异常情况，确保数据完整性

# 函数：安全地计算统计数据
calculate_stats() {
    local file="$1"
    local latency_values="$2"

    # 过滤掉空行和非数字行，确保数据质量
    local clean_values=$(echo "$latency_values" | grep -E '^[0-9]+$' | grep -v '^$')

    if [ -z "$clean_values" ]; then
        echo "$file,0,0.00,0.00"
        return
    fi

    # 计算条数
    local count=$(echo "$clean_values" | wc -l | tr -d ' ')

    # 如果条数为0，直接返回
    if [ "$count" -eq 0 ]; then
        echo "$file,0,0.00,0.00"
        return
    fi

    # 计算平均值（使用awk确保精度）
    local avg=$(echo "$clean_values" | awk '{sum += $1; count++} END {if(count > 0) printf "%.2f", sum/count; else print "0.00"}')

    # 计算标准差（只有当条数大于1时才计算标准差）
    local std_dev
    if [ "$count" -gt 1 ]; then
        std_dev=$(echo "$clean_values" | awk -v avg="$avg" '{sum += ($1 - avg)^2} END {if(NR > 1) printf "%.2f", sqrt(sum/(NR-1)); else print "0.00"}')
    else
        std_dev="0.00"
    fi

    echo "$file,$count,$avg,$std_dev"
}

echo "文件,条数,latency平均值,latency标准差"

for file in test2_1.log test2_2.log test2_4.log test2_5.log test2_6.log; do
    echo "正在处理 $file..." >&2

    # 检查文件是否存在且可读
    if [ ! -f "$file" ]; then
        echo "警告: 文件 $file 不存在" >&2
        echo "$file,0,0.00,0.00"
        continue
    fi

    if [ ! -r "$file" ]; then
        echo "警告: 文件 $file 不可读" >&2
        echo "$file,0,0.00,0.00"
        continue
    fi

    # 检查文件是否为空
    if [ ! -s "$file" ]; then
        echo "警告: 文件 $file 为空" >&2
        echo "$file,0,0.00,0.00"
        continue
    fi

    # 提取latency数值（更健壮的正则表达式）
    # 支持多种格式：latency: 123, latency:123, latency: 123ms等
    latency_values=$(grep -i "latency" "$file" 2>/dev/null | sed -n 's/.*latency[[:space:]]*:[[:space:]]*\([0-9][0-9]*\).*/\1/p')

    # 检查是否成功提取到数据
    if [ -z "$latency_values" ]; then
        echo "警告: 文件 $file 中未找到有效的latency数据" >&2
        echo "$file,0,0.00,0.00"
        continue
    fi

    # 调用函数计算统计数据
    calculate_stats "$file" "$latency_values"
done

echo "处理完成！" >&2
